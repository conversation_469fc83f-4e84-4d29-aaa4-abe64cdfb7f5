"""
Sistema di normalizzazione intelligente per sezioni di cavi.
Versione integrata nel backend per uso automatico.
"""

import re
import logging
from typing import Tuple

class CableNormalizer:
    """
    Classe per la normalizzazione intelligente delle sezioni di cavi.
    
    Gestisce:
    - Case sensitivity (x vs X)
    - Separatori decimali (, vs .)
    - Spazi e formattazione
    - Unità di misura (MM2, mm2, MM², mm²)
    - Parentesi e caratteri speciali
    - Suffissi (+SH, +sh, etc.)
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Pattern per riconoscere diverse parti della sezione
        self.patterns = {
            # Pattern principale: cattura numero conduttori, separatore, sezione, unità, suffisso complesso
            'main': re.compile(
                r'^\s*\(?(\d+)\s*[xX×]\s*(\d+(?:[.,]\d+)?)\s*(mm2?|MM2?|mm²|MM²)?\s*(\+\s*(?:\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?[a-zA-Z0-9]*(?:\s*[a-zA-Z0-9]*)*)?.*?\)?\s*$',
                re.IGNORECASE
            ),
            
            # Pattern per sezioni senza conduttori (es: "240MM2")
            'simple': re.compile(
                r'^\s*\(?(\d+(?:[.,]\d+)?)\s*(mm2?|MM2?|mm²|MM²)?\s*(\+\s*(?:\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?[a-zA-Z0-9]*(?:\s*[a-zA-Z0-9]*)*)?.*?\)?\s*$',
                re.IGNORECASE
            ),
            
            # Pattern per suffissi complessi (es: "+2.5YG", "+SH")
            'suffix': re.compile(r'\+\s*(\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?([a-zA-Z0-9]+(?:\s*[a-zA-Z0-9]*)*)', re.IGNORECASE)
        }
        
        # Mappatura unità di misura standard
        self.unit_mapping = {
            'mm2': 'MM2',
            'mm²': 'MM2',
            'MM²': 'MM2',
            'MM2': 'MM2',
            '': 'MM2'  # Default se non specificato
        }
        
        # Mappatura suffissi standard
        self.suffix_mapping = {
            'sh': 'SH',
            'yg': 'YG',
            'ye': 'YE',
            'gn': 'GN',
            'pe': 'PE',
            'pvc': 'PVC'
        }
    
    def normalize_decimal(self, value: str) -> str:
        """Normalizza i separatori decimali."""
        # Sostituisce virgola con punto
        normalized = value.replace(',', '.')
        
        # Rimuove zeri trailing dopo il punto decimale
        if '.' in normalized:
            normalized = normalized.rstrip('0').rstrip('.')
        
        return normalized
    
    def normalize_unit(self, unit: str) -> str:
        """Normalizza le unità di misura."""
        if not unit:
            return 'MM2'
        
        unit_clean = unit.strip().lower()
        return self.unit_mapping.get(unit_clean, 'MM2')
    
    def normalize_suffix(self, suffix: str) -> str:
        """Normalizza i suffissi (+SH, +YG, +2.5YG, etc.)."""
        if not suffix:
            return ''
        
        # Usa il pattern per estrarre numero e lettere
        match = self.patterns['suffix'].match(suffix)
        if match:
            number_part = match.group(1) or ''
            letter_part = match.group(2) or ''
            
            # Normalizza la parte numerica se presente
            if number_part:
                number_part = self.normalize_decimal(number_part.replace('mm2', '').replace('MM2', '').replace('mm²', '').replace('MM²', ''))
            
            # Normalizza la parte letterale
            letter_normalized = self.suffix_mapping.get(letter_part.lower(), letter_part.upper())
            
            return f"+{number_part}{letter_normalized}" if number_part or letter_normalized else ''
        
        # Fallback: rimuove il + iniziale e spazi, normalizza
        suffix_clean = suffix.replace('+', '').strip()
        parts = []
        for part in suffix_clean.split():
            part_lower = part.lower()
            normalized_part = self.suffix_mapping.get(part_lower, part.upper())
            parts.append(normalized_part)
        
        return '+' + ''.join(parts) if parts else ''
    
    def normalize_section(self, section: str) -> str:
        """
        Normalizza una sezione di cavo.
        
        Args:
            section: Sezione da normalizzare (es: "1x240 mm2", "4x1,5+sh")
            
        Returns:
            Sezione normalizzata (es: "1X240MM2", "4X1.5+SH")
        """
        if not section or not isinstance(section, str):
            return ''
        
        # Rimuove spazi extra e caratteri speciali all'inizio/fine
        section_clean = section.strip()
        
        # Prova prima il pattern principale (con conduttori)
        match = self.patterns['main'].match(section_clean)
        
        if match:
            conductors = match.group(1)
            wire_section = match.group(2)
            unit = match.group(3) or ''
            suffix = match.group(4) or ''
            
            # Normalizza ogni componente
            normalized_section = self.normalize_decimal(wire_section)
            normalized_unit = self.normalize_unit(unit)
            normalized_suffix = self.normalize_suffix(suffix)
            
            # Costruisce la sezione normalizzata
            result = f"{conductors}X{normalized_section}{normalized_unit}{normalized_suffix}"
            
            self.logger.debug(f"Normalized '{section}' -> '{result}' (main pattern)")
            return result
        
        # Prova il pattern semplice (senza conduttori)
        match = self.patterns['simple'].match(section_clean)
        
        if match:
            wire_section = match.group(1)
            unit = match.group(2) or ''
            suffix = match.group(3) or ''
            
            # Normalizza ogni componente
            normalized_section = self.normalize_decimal(wire_section)
            normalized_unit = self.normalize_unit(unit)
            normalized_suffix = self.normalize_suffix(suffix)
            
            # Costruisce la sezione normalizzata
            result = f"{normalized_section}{normalized_unit}{normalized_suffix}"
            
            self.logger.debug(f"Normalized '{section}' -> '{result}' (simple pattern)")
            return result
        
        # Se non matcha nessun pattern, restituisce la stringa pulita in maiuscolo
        result = section_clean.upper().replace(' ', '')
        self.logger.warning(f"Could not parse '{section}', returning cleaned: '{result}'")
        return result
    
    def normalize_tipologia(self, tipologia: str) -> str:
        """Normalizza la tipologia del cavo."""
        if not tipologia or not isinstance(tipologia, str):
            return ''
        
        # Rimuove spazi extra e converte in maiuscolo
        return tipologia.strip().upper()
    
    def normalize_cable_data(self, tipologia: str, sezione: str) -> Tuple[str, str]:
        """
        Normalizza sia tipologia che sezione.
        
        Args:
            tipologia: Tipologia del cavo
            sezione: Sezione del cavo
            
        Returns:
            Tupla (tipologia_normalizzata, sezione_normalizzata)
        """
        normalized_tipologia = self.normalize_tipologia(tipologia)
        normalized_sezione = self.normalize_section(sezione)
        
        return normalized_tipologia, normalized_sezione

# Istanza globale del normalizzatore
_normalizer = CableNormalizer()

# Funzioni di utilità per l'integrazione nel sistema
def normalize_cable_section(section: str) -> str:
    """Funzione di utilità per normalizzare una sezione."""
    return _normalizer.normalize_section(section)

def normalize_cable_type(tipologia: str) -> str:
    """Funzione di utilità per normalizzare una tipologia."""
    return _normalizer.normalize_tipologia(tipologia)

def normalize_cable_data(tipologia: str, sezione: str) -> Tuple[str, str]:
    """Funzione di utilità per normalizzare tipologia e sezione insieme."""
    return _normalizer.normalize_cable_data(tipologia, sezione)
