"""
Sistema di normalizzazione completo e sicuro per tutti i campi del sistema CMS.
Previene SQL injection, XSS, e garantisce consistenza dei dati.
Versione integrata nel backend per uso automatico.
"""

import re
import logging
import html
import unicodedata
from typing import Tuple, Optional, Dict, Any

class CableNormalizer:
    """
    Classe per la normalizzazione completa e sicura di tutti i campi del sistema CMS.

    Gestisce:
    - Sicurezza: Prevenzione SQL injection, XSS, caratteri pericolosi
    - Sezioni cavi: Case sensitivity, separatori decimali, unità di misura, suffissi
    - Campi testo: Normalizzazione maiuscolo, rimozione spazi, caratteri speciali
    - Consistenza: Standardizzazione di tutti i formati di input
    - Validazione: Controllo lunghezza e caratteri permessi
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Pattern di sicurezza - caratteri pericolosi da rimuovere/sostituire
        self.dangerous_patterns = {
            # SQL injection patterns
            'sql_injection': re.compile(r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b|--|\/\*|\*\/|;|\'|"|`)', re.IGNORECASE),
            # XSS patterns
            'xss': re.compile(r'(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|onmouseover=)', re.IGNORECASE),
            # Caratteri di controllo e non stampabili
            'control_chars': re.compile(r'[\x00-\x1f\x7f-\x9f]'),
            # Caratteri Unicode pericolosi
            'unicode_dangerous': re.compile(r'[\u200b-\u200f\u202a-\u202e\u2060-\u206f]'),
        }

        # Caratteri permessi per diversi tipi di campo
        self.allowed_patterns = {
            'alphanumeric_extended': re.compile(r'^[A-Z0-9\s\-_+.,:()\/\\]*$'),
            'cable_id': re.compile(r'^[A-Z0-9\-_]*$'),
            'numeric_decimal': re.compile(r'^[0-9.,]*$'),
            'text_safe': re.compile(r'^[A-Z0-9\s\-_+.,:()\/\\àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]*$', re.IGNORECASE),
        }

        # Limiti di lunghezza per i campi
        self.field_limits = {
            'id_cavo': 50,
            'tipologia': 100,
            'sezione': 100,
            'utility': 50,
            'colore_cavo': 30,
            'sistema': 50,
            'ubicazione': 200,
            'utenza': 100,
            'descrizione': 500,
            'responsabile': 100,
            'comanda': 50,
            'default': 255
        }

        # Pattern per riconoscere diverse parti della sezione
        self.patterns = {
            # Pattern principale: cattura numero conduttori, separatore, sezione, unità, suffisso complesso
            'main': re.compile(
                r'^\s*\(?(\d+)\s*[xX×]\s*(\d+(?:[.,]\d+)?)\s*(mm2?|MM2?|mm²|MM²)?\s*(\+\s*(?:\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?[a-zA-Z0-9]*(?:\s*[a-zA-Z0-9]*)*)?.*?\)?\s*$',
                re.IGNORECASE
            ),
            
            # Pattern per sezioni senza conduttori (es: "240MM2")
            'simple': re.compile(
                r'^\s*\(?(\d+(?:[.,]\d+)?)\s*(mm2?|MM2?|mm²|MM²)?\s*(\+\s*(?:\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?[a-zA-Z0-9]*(?:\s*[a-zA-Z0-9]*)*)?.*?\)?\s*$',
                re.IGNORECASE
            ),
            
            # Pattern per suffissi complessi (es: "+2.5YG", "+SH")
            'suffix': re.compile(r'\+\s*(\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?([a-zA-Z0-9]+(?:\s*[a-zA-Z0-9]*)*)', re.IGNORECASE)
        }
        
        # Mappatura unità di misura standard
        self.unit_mapping = {
            'mm2': 'MM2',
            'mm²': 'MM2',
            'MM²': 'MM2',
            'MM2': 'MM2',
            '': 'MM2'  # Default se non specificato
        }
        
        # Mappatura suffissi standard
        self.suffix_mapping = {
            'sh': 'SH',
            'yg': 'YG',
            'ye': 'YE',
            'gn': 'GN',
            'pe': 'PE',
            'pvc': 'PVC'
        }

    def sanitize_input(self, value: str, field_type: str = 'default') -> str:
        """
        Sanitizza l'input rimuovendo caratteri pericolosi e normalizzando.

        Args:
            value: Valore da sanitizzare
            field_type: Tipo di campo per applicare regole specifiche

        Returns:
            Valore sanitizzato e sicuro
        """
        if not value or not isinstance(value, str):
            return ''

        # 1. Decodifica HTML entities
        value = html.unescape(value)

        # 2. Normalizza Unicode (rimuove accenti, converte caratteri speciali)
        value = unicodedata.normalize('NFKD', value)

        # 3. Rimuove caratteri pericolosi
        for pattern_name, pattern in self.dangerous_patterns.items():
            if pattern.search(value):
                self.logger.warning(f"Caratteri pericolosi rilevati ({pattern_name}): {value}")
                value = pattern.sub('', value)

        # 3.1. Rimuove tutti i tag HTML rimanenti
        value = re.sub(r'<[^>]*>', '', value)

        # 3.2. Rimuove caratteri di controllo e non stampabili
        value = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', value)

        # 4. Rimuove spazi multipli e normalizza
        value = re.sub(r'\s+', ' ', value).strip()

        # 5. Applica limiti di lunghezza
        max_length = self.field_limits.get(field_type, self.field_limits['default'])
        if len(value) > max_length:
            self.logger.warning(f"Campo {field_type} troncato da {len(value)} a {max_length} caratteri")
            value = value[:max_length].strip()

        return value

    def normalize_text_field(self, value: str, field_type: str = 'default', uppercase: bool = True) -> str:
        """
        Normalizza un campo di testo generico.

        Args:
            value: Valore da normalizzare
            field_type: Tipo di campo
            uppercase: Se convertire in maiuscolo

        Returns:
            Valore normalizzato
        """
        if not value or not isinstance(value, str):
            return ''

        # Sanitizza prima di tutto
        value = self.sanitize_input(value, field_type)

        # Converte in maiuscolo se richiesto
        if uppercase:
            value = value.upper()

        # Rimuove spazi extra
        value = re.sub(r'\s+', ' ', value).strip()

        return value

    def normalize_cable_id(self, value: str) -> str:
        """Normalizza un ID cavo."""
        if not value or not isinstance(value, str):
            return ''

        # Sanitizza e converte in maiuscolo
        value = self.sanitize_input(value, 'id_cavo')
        value = value.upper()

        # Rimuove spazi e caratteri non permessi
        value = re.sub(r'[^A-Z0-9\-_]', '', value)

        return value
    
    def normalize_decimal(self, value: str) -> str:
        """Normalizza i separatori decimali."""
        # Sostituisce virgola con punto
        normalized = value.replace(',', '.')
        
        # Rimuove zeri trailing dopo il punto decimale
        if '.' in normalized:
            normalized = normalized.rstrip('0').rstrip('.')
        
        return normalized
    
    def normalize_unit(self, unit: str) -> str:
        """Normalizza le unità di misura."""
        if not unit:
            return 'MM2'
        
        unit_clean = unit.strip().lower()
        return self.unit_mapping.get(unit_clean, 'MM2')
    
    def normalize_suffix(self, suffix: str) -> str:
        """Normalizza i suffissi (+SH, +YG, +2.5YG, etc.)."""
        if not suffix:
            return ''
        
        # Usa il pattern per estrarre numero e lettere
        match = self.patterns['suffix'].match(suffix)
        if match:
            number_part = match.group(1) or ''
            letter_part = match.group(2) or ''
            
            # Normalizza la parte numerica se presente
            if number_part:
                number_part = self.normalize_decimal(number_part.replace('mm2', '').replace('MM2', '').replace('mm²', '').replace('MM²', ''))
            
            # Normalizza la parte letterale
            letter_normalized = self.suffix_mapping.get(letter_part.lower(), letter_part.upper())
            
            return f"+{number_part}{letter_normalized}" if number_part or letter_normalized else ''
        
        # Fallback: rimuove il + iniziale e spazi, normalizza
        suffix_clean = suffix.replace('+', '').strip()
        parts = []
        for part in suffix_clean.split():
            part_lower = part.lower()
            normalized_part = self.suffix_mapping.get(part_lower, part.upper())
            parts.append(normalized_part)
        
        return '+' + ''.join(parts) if parts else ''
    
    def normalize_section(self, section: str) -> str:
        """
        Normalizza una sezione di cavo.
        
        Args:
            section: Sezione da normalizzare (es: "1x240 mm2", "4x1,5+sh")
            
        Returns:
            Sezione normalizzata (es: "1X240MM2", "4X1.5+SH")
        """
        if not section or not isinstance(section, str):
            return ''
        
        # Rimuove spazi extra e caratteri speciali all'inizio/fine
        section_clean = section.strip()
        
        # Prova prima il pattern principale (con conduttori)
        match = self.patterns['main'].match(section_clean)
        
        if match:
            conductors = match.group(1)
            wire_section = match.group(2)
            unit = match.group(3) or ''
            suffix = match.group(4) or ''
            
            # Normalizza ogni componente
            normalized_section = self.normalize_decimal(wire_section)
            normalized_unit = self.normalize_unit(unit)
            normalized_suffix = self.normalize_suffix(suffix)
            
            # Costruisce la sezione normalizzata
            result = f"{conductors}X{normalized_section}{normalized_unit}{normalized_suffix}"
            
            self.logger.debug(f"Normalized '{section}' -> '{result}' (main pattern)")
            return result
        
        # Prova il pattern semplice (senza conduttori)
        match = self.patterns['simple'].match(section_clean)
        
        if match:
            wire_section = match.group(1)
            unit = match.group(2) or ''
            suffix = match.group(3) or ''
            
            # Normalizza ogni componente
            normalized_section = self.normalize_decimal(wire_section)
            normalized_unit = self.normalize_unit(unit)
            normalized_suffix = self.normalize_suffix(suffix)
            
            # Costruisce la sezione normalizzata
            result = f"{normalized_section}{normalized_unit}{normalized_suffix}"
            
            self.logger.debug(f"Normalized '{section}' -> '{result}' (simple pattern)")
            return result
        
        # Se non matcha nessun pattern, restituisce la stringa pulita in maiuscolo
        result = section_clean.upper().replace(' ', '')
        self.logger.warning(f"Could not parse '{section}', returning cleaned: '{result}'")
        return result
    
    def normalize_tipologia(self, tipologia: str) -> str:
        """Normalizza la tipologia del cavo."""
        if not tipologia or not isinstance(tipologia, str):
            return ''

        # Usa la sanitizzazione completa prima di normalizzare
        tipologia = self.sanitize_input(tipologia, 'tipologia')

        # Rimuove spazi extra e converte in maiuscolo
        return tipologia.strip().upper()
    
    def normalize_cable_data(self, tipologia: str, sezione: str) -> Tuple[str, str]:
        """
        Normalizza sia tipologia che sezione.

        Args:
            tipologia: Tipologia del cavo
            sezione: Sezione del cavo

        Returns:
            Tupla (tipologia_normalizzata, sezione_normalizzata)
        """
        normalized_tipologia = self.normalize_tipologia(tipologia)
        normalized_sezione = self.normalize_section(sezione)

        return normalized_tipologia, normalized_sezione

    def normalize_all_cable_fields(self, cable_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalizza tutti i campi di un cavo per sicurezza e consistenza.

        Args:
            cable_data: Dizionario con i dati del cavo

        Returns:
            Dizionario con tutti i campi normalizzati
        """
        normalized = {}

        # Campi che richiedono normalizzazione specifica
        field_normalizers = {
            'id_cavo': lambda x: self.normalize_cable_id(x),
            'tipologia': lambda x: self.normalize_tipologia(x),
            'sezione': lambda x: self.normalize_section(x),
            'utility': lambda x: self.normalize_text_field(x, 'utility', uppercase=True),
            'colore_cavo': lambda x: self.normalize_text_field(x, 'colore_cavo', uppercase=True),
            'sistema': lambda x: self.normalize_text_field(x, 'sistema', uppercase=True),
            'ubicazione_partenza': lambda x: self.normalize_text_field(x, 'ubicazione', uppercase=True),
            'ubicazione_arrivo': lambda x: self.normalize_text_field(x, 'ubicazione', uppercase=True),
            'utenza_partenza': lambda x: self.normalize_text_field(x, 'utenza', uppercase=True),
            'utenza_arrivo': lambda x: self.normalize_text_field(x, 'utenza', uppercase=True),
            'descrizione_utenza_partenza': lambda x: self.normalize_text_field(x, 'descrizione', uppercase=False),
            'descrizione_utenza_arrivo': lambda x: self.normalize_text_field(x, 'descrizione', uppercase=False),
            'responsabile_posa': lambda x: self.normalize_text_field(x, 'responsabile', uppercase=True),
            'responsabile_partenza': lambda x: self.normalize_text_field(x, 'responsabile', uppercase=True),
            'responsabile_arrivo': lambda x: self.normalize_text_field(x, 'responsabile', uppercase=True),
            'comanda_posa': lambda x: self.normalize_text_field(x, 'comanda', uppercase=True),
            'comanda_partenza': lambda x: self.normalize_text_field(x, 'comanda', uppercase=True),
            'comanda_arrivo': lambda x: self.normalize_text_field(x, 'comanda', uppercase=True),
            'revisione_ufficiale': lambda x: self.normalize_text_field(x, 'default', uppercase=True),
            'stato_installazione': lambda x: self.normalize_text_field(x, 'default', uppercase=True),
            'id_bobina': lambda x: self.normalize_text_field(x, 'default', uppercase=True),
        }

        # Campi numerici che non richiedono normalizzazione speciale
        numeric_fields = {'metri_teorici', 'metratura_reale', 'n_conduttori', 'sh', 'collegamenti', 'modificato_manualmente'}

        # Campi di sistema che non devono essere modificati
        system_fields = {'id_cantiere', 'timestamp', 'data_posa'}

        # Normalizza ogni campo
        for field, value in cable_data.items():
            if field in system_fields:
                # Campi di sistema: copia senza modifiche
                normalized[field] = value
            elif field in field_normalizers:
                # Campi con normalizzazione specifica
                if value is not None:
                    try:
                        normalized[field] = field_normalizers[field](str(value))
                    except Exception as e:
                        self.logger.error(f"Errore normalizzazione campo {field}: {e}")
                        normalized[field] = self.sanitize_input(str(value) if value is not None else '')
                else:
                    normalized[field] = None
            elif field in numeric_fields:
                # Campi numerici: sanitizza solo se stringa
                if isinstance(value, str):
                    normalized[field] = self.sanitize_input(value, 'numeric_decimal')
                else:
                    normalized[field] = value
            else:
                # Altri campi: normalizzazione generica
                if isinstance(value, str):
                    normalized[field] = self.normalize_text_field(value, 'default', uppercase=True)
                else:
                    normalized[field] = value

        return normalized

# Istanza globale del normalizzatore
_normalizer = CableNormalizer()

# Funzioni di utilità per l'integrazione nel sistema
def normalize_cable_section(section: str) -> str:
    """Funzione di utilità per normalizzare una sezione."""
    return _normalizer.normalize_section(section)

def normalize_cable_type(tipologia: str) -> str:
    """Funzione di utilità per normalizzare una tipologia."""
    return _normalizer.normalize_tipologia(tipologia)

def normalize_cable_data(tipologia: str, sezione: str) -> Tuple[str, str]:
    """Funzione di utilità per normalizzare tipologia e sezione insieme."""
    return _normalizer.normalize_cable_data(tipologia, sezione)

def normalize_all_cable_fields(cable_data: Dict[str, Any]) -> Dict[str, Any]:
    """Funzione di utilità per normalizzare tutti i campi di un cavo."""
    return _normalizer.normalize_all_cable_fields(cable_data)

def sanitize_input(value: str, field_type: str = 'default') -> str:
    """Funzione di utilità per sanitizzare un input."""
    return _normalizer.sanitize_input(value, field_type)

def normalize_text_field(value: str, field_type: str = 'default', uppercase: bool = True) -> str:
    """Funzione di utilità per normalizzare un campo di testo."""
    return _normalizer.normalize_text_field(value, field_type, uppercase)
