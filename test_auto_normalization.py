#!/usr/bin/env python3
"""
Test per verificare che la normalizzazione automatica funzioni negli endpoint.
"""

import requests
import json
import sys

# Configurazione
API_URL = "http://localhost:8002/api"
USERNAME = "a"
PASSWORD = "a"

def login():
    """Effettua il login e restituisce il token."""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{API_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"✅ Login effettuato con successo")
        return token
    else:
        print(f"❌ Errore durante il login: {response.status_code}")
        print(response.text)
        return None

def test_create_cavo_with_normalization(token):
    """Testa la creazione di un cavo con normalizzazione automatica."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test case: cavo con dati non normalizzati e potenzialmente pericolosi
    test_cavo = {
        "id_cavo": "test_norm_001",  # minuscolo
        "revisione_ufficiale": "00",
        "sistema": "test system",  # con spazi
        "utility": "test utility",  # minuscolo con spazi
        "colore_cavo": "nero   ",  # minuscolo con spazi extra
        "tipologia": "fg16or16",  # minuscolo
        "n_conduttori": "4",
        "sezione": "1x240 mm2",  # con spazi e minuscolo
        "sh": "False",
        "ubicazione_partenza": "test  start location",  # spazi multipli
        "utenza_partenza": "test user start",  # minuscolo
        "descrizione_utenza_partenza": "Test Start Description with àccénts",  # accenti
        "ubicazione_arrivo": "TEST_END",
        "utenza_arrivo": "test_user_end",  # minuscolo con underscore
        "descrizione_utenza_arrivo": "Test End Description",
        "metri_teorici": 100.0,
        "metratura_reale": 0.0,
        "responsabile_posa": "test worker",  # minuscolo con spazi
        "id_bobina": "bobina_vuota",  # minuscolo
        "stato_installazione": "da installare"  # minuscolo
    }
    
    print(f"\n🧪 TEST CREAZIONE CAVO CON NORMALIZZAZIONE COMPLETA")
    print(f"   ID originale: '{test_cavo['id_cavo']}'")
    print(f"   Tipologia originale: '{test_cavo['tipologia']}'")
    print(f"   Sezione originale: '{test_cavo['sezione']}'")
    print(f"   Utility originale: '{test_cavo['utility']}'")
    print(f"   Colore originale: '{test_cavo['colore_cavo']}'")

    response = requests.post(
        f"{API_URL}/cavi/1",
        headers=headers,
        json=test_cavo
    )

    if response.status_code == 200:
        cavo_creato = response.json()
        print(f"✅ Cavo creato con successo!")
        print(f"   ID normalizzato: '{cavo_creato['id_cavo']}'")
        print(f"   Tipologia normalizzata: '{cavo_creato['tipologia']}'")
        print(f"   Sezione normalizzata: '{cavo_creato['sezione']}'")
        print(f"   Utility normalizzata: '{cavo_creato['utility']}'")
        print(f"   Colore normalizzato: '{cavo_creato['colore_cavo']}'")
        print(f"   Sistema normalizzato: '{cavo_creato['sistema']}'")
        print(f"   Ubicazione partenza: '{cavo_creato['ubicazione_partenza']}'")
        print(f"   Stato installazione: '{cavo_creato['stato_installazione']}'")

        # Verifica che la normalizzazione sia avvenuta
        expected_values = {
            'id_cavo': 'TEST_NORM_001',
            'tipologia': 'FG16OR16',
            'sezione': '1X240MM2',
            'utility': 'TEST UTILITY',
            'colore_cavo': 'NERO',
            'sistema': 'TEST SYSTEM',
            'ubicazione_partenza': 'TEST START LOCATION',
            'utenza_partenza': 'TEST USER START',
            'responsabile_posa': 'TEST WORKER',
            'id_bobina': 'BOBINA_VUOTA',
            'stato_installazione': 'DA INSTALLARE'
        }

        all_correct = True
        for field, expected in expected_values.items():
            actual = cavo_creato.get(field)
            if actual != expected:
                print(f"❌ Campo {field}: atteso '{expected}', ottenuto '{actual}'")
                all_correct = False

        if all_correct:
            print(f"✅ Normalizzazione automatica completa funziona correttamente!")
            return cavo_creato['id_cavo']
        else:
            print(f"❌ Alcuni campi non sono stati normalizzati correttamente")
            return None
    else:
        print(f"❌ Errore durante la creazione del cavo: {response.status_code}")
        print(f"   Errore: {response.text}")
        return None

def test_update_cavo_with_normalization(token, cavo_id):
    """Testa l'aggiornamento di un cavo con normalizzazione automatica."""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test case: aggiornamento con sezione non normalizzata
    update_data = {
        "tipologia": "liycy",  # minuscolo
        "sezione": "3x2,5+2,5yg"  # virgola e minuscolo
    }
    
    print(f"\n🧪 TEST AGGIORNAMENTO CAVO CON NORMALIZZAZIONE")
    print(f"   Tipologia da aggiornare: '{update_data['tipologia']}'")
    print(f"   Sezione da aggiornare: '{update_data['sezione']}'")
    
    response = requests.put(
        f"{API_URL}/cavi/1/{cavo_id}",
        headers=headers,
        json=update_data
    )
    
    if response.status_code == 200:
        cavo_aggiornato = response.json()
        print(f"✅ Cavo aggiornato con successo!")
        print(f"   Tipologia normalizzata: '{cavo_aggiornato['tipologia']}'")
        print(f"   Sezione normalizzata: '{cavo_aggiornato['sezione']}'")
        
        # Verifica che la normalizzazione sia avvenuta
        expected_tipologia = "LIYCY"
        expected_sezione = "3X2.5MM2+2.5YG"
        
        if cavo_aggiornato['tipologia'] == expected_tipologia and cavo_aggiornato['sezione'] == expected_sezione:
            print(f"✅ Normalizzazione automatica nell'aggiornamento funziona correttamente!")
            return True
        else:
            print(f"❌ Normalizzazione nell'aggiornamento non funziona:")
            print(f"   Atteso: tipologia='{expected_tipologia}', sezione='{expected_sezione}'")
            print(f"   Ottenuto: tipologia='{cavo_aggiornato['tipologia']}', sezione='{cavo_aggiornato['sezione']}'")
            return False
    else:
        print(f"❌ Errore durante l'aggiornamento del cavo: {response.status_code}")
        print(f"   Errore: {response.text}")
        return False

def cleanup_test_cavo(token, cavo_id):
    """Pulisce il cavo di test."""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.delete(f"{API_URL}/cavi/1/{cavo_id}", headers=headers)
    if response.status_code == 200:
        print(f"✅ Cavo di test {cavo_id} eliminato")
    else:
        print(f"⚠️  Impossibile eliminare il cavo di test {cavo_id}")

def main():
    """Funzione principale del test."""
    print("🧪 TEST NORMALIZZAZIONE AUTOMATICA NEGLI ENDPOINT")
    print("=" * 60)
    
    # 1. Login
    token = login()
    if not token:
        sys.exit(1)
    
    # 2. Test creazione con normalizzazione
    cavo_id = test_create_cavo_with_normalization(token)
    if not cavo_id:
        print("❌ Test creazione fallito")
        sys.exit(1)
    
    # 3. Test aggiornamento con normalizzazione
    update_success = test_update_cavo_with_normalization(token, cavo_id)
    if not update_success:
        print("❌ Test aggiornamento fallito")
        cleanup_test_cavo(token, cavo_id)
        sys.exit(1)
    
    # 4. Cleanup
    cleanup_test_cavo(token, cavo_id)
    
    print(f"\n🎉 TUTTI I TEST COMPLETATI CON SUCCESSO!")
    print("✅ La normalizzazione automatica funziona correttamente negli endpoint!")

if __name__ == "__main__":
    main()
