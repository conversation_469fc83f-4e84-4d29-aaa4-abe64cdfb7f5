from typing import Any, List, Optional, Dict
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session
from sqlalchemy import inspect, text

from backend.database import get_db, engine
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.cavo import Cavo
from backend.models.parco_cavi import ParcoCavo
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.schemas.cavo import CavoCreate, CavoInDB, CavoUpdate, MetriPosatiUpdate, BobinaUpdate, CollegamentoUpdate, MarkAsSpareUpdate, DeleteCavoOptions
from backend.core.security import get_current_active_user
from backend.utils.cable_normalizer import normalize_cable_data

router = APIRouter()

# Costanti per i flag di collegamento
COLLEGAMENTO_PARTENZA = 1  # 0001 in binario
COLLEGAMENTO_ARRIVO = 2    # 0010 in binario





@router.get("/{cantiere_id}/check/{id_cavo}")
def check_cavo_exists(
    cantiere_id: int,
    id_cavo: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Verifica se un cavo con l'ID specificato esiste già nel cantiere.

    Args:
        cantiere_id: ID del cantiere
        id_cavo: ID del cavo da verificare
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Dizionario con il risultato della verifica
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Normalizza l'ID del cavo (converti in maiuscolo)
        id_cavo = id_cavo.upper()

        # Verifica se il cavo esiste già (sia attivo che SPARE)
        existing_cavo = db.query(Cavo).filter(
            Cavo.id_cavo == id_cavo,
            Cavo.id_cantiere == cantiere_id
        ).first()

        return {
            "exists": existing_cavo is not None,
            "is_spare": existing_cavo.modificato_manualmente == 3 if existing_cavo else False
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la verifica del cavo: {str(e)}"
        )

@router.get("/{cantiere_id}/revisione-corrente")
def get_revisione_corrente(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera la revisione ufficiale corrente del cantiere.
    Ritorna la revisione più recente o '00' se non ce ne sono.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Dizionario con la revisione corrente
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Esegui la query per trovare la revisione più recente
        result = db.execute(
            text("""
                SELECT revisione_ufficiale
                FROM cavi
                WHERE id_cantiere = :cantiere_id
                AND revisione_ufficiale != '00'
                ORDER BY
                    CASE
                        WHEN revisione_ufficiale LIKE 'REV_%' THEN substring(revisione_ufficiale from 5)
                        ELSE '0'
                    END DESC
                LIMIT 1
            """),
            {"cantiere_id": cantiere_id}
        ).fetchone()

        revisione = result[0] if result else '00'
        return {"revisione_corrente": revisione}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il recupero della revisione corrente: {str(e)}"
        )

@router.get("/{cantiere_id}/revisioni")
def get_revisioni_disponibili(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutte le revisioni disponibili per il cantiere.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Lista delle revisioni disponibili
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Esegui la query per trovare tutte le revisioni
        result = db.execute(
            text("""
                SELECT DISTINCT revisione_ufficiale, COUNT(*) as cavi_count
                FROM cavi
                WHERE id_cantiere = :cantiere_id
                AND revisione_ufficiale IS NOT NULL
                AND revisione_ufficiale != ''
                GROUP BY revisione_ufficiale
                ORDER BY
                    CASE
                        WHEN revisione_ufficiale LIKE 'REV_%' THEN substring(revisione_ufficiale from 5)
                        ELSE revisione_ufficiale
                    END DESC
            """),
            {"cantiere_id": cantiere_id}
        ).fetchall()

        revisioni = [{"revisione": row[0], "cavi_count": row[1]} for row in result]
        return {"revisioni": revisioni}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero della revisione corrente: {str(e)}"
        )

@router.get("/debug/{cantiere_id}")
def debug_cavi(cantiere_id: int, db: Session = Depends(get_db)):
    """Endpoint di debug per verificare i cavi direttamente nel database."""
    try:
        # Esegui una query SQL diretta per ottenere i cavi
        result = db.execute(
            text("""
            SELECT id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo, tipologia,
                   n_conduttori, sezione, sh, ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                   ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo, metri_teorici, metratura_reale,
                   responsabile_posa, id_bobina, stato_installazione, modificato_manualmente, timestamp
            FROM cavi
            WHERE id_cantiere = :cantiere_id
            ORDER BY id_cavo
            """),
            {"cantiere_id": cantiere_id}
        )

        # Converti il risultato in una lista di dizionari
        rows = result.fetchall()

        # Debug: Stampa il tipo di dati restituito
        row_type = type(rows[0]) if rows else "Nessuna riga trovata"
        print(f"Tipo di dato restituito: {row_type}")

        # Converti le righe in dizionari in modo sicuro
        cavi = []
        for row in rows:
            try:
                # Usa _mapping per la conversione (metodo più affidabile)
                if hasattr(row, '_mapping'):
                    cavo_dict = dict(row._mapping)
                elif isinstance(row, tuple) and hasattr(row, '_fields'):
                    # NamedTuple
                    cavo_dict = row._asdict()
                else:
                    # Fallback: stampa informazioni dettagliate
                    print(f"ATTENZIONE: Impossibile convertire la riga in dizionario: {row}")
                    print(f"Tipo: {type(row)}, Attributi: {dir(row) if hasattr(row, '__dict__') else 'N/A'}")
                    # Prova a creare un dizionario manualmente
                    cavo_dict = {}
                    if isinstance(row, (list, tuple)):
                        # Ottieni i nomi delle colonne dalla query
                        column_names = result.keys()
                        if len(column_names) == len(row):
                            cavo_dict = dict(zip(column_names, row))
                        else:
                            print(f"ERRORE: Il numero di colonne ({len(column_names)}) non corrisponde al numero di valori ({len(row)})")
                            continue
                    else:
                        continue
                cavi.append(cavo_dict)
            except Exception as e:
                print(f"Errore durante la conversione della riga in dizionario: {e}")
                print(f"Riga problematica: {row}")
                import traceback
                traceback.print_exc()

        # Conta i cavi attivi e spare
        cavi_attivi = [c for c in cavi if c.get('modificato_manualmente') != 3 or c.get('modificato_manualmente') is None]
        cavi_spare = [c for c in cavi if c.get('modificato_manualmente') == 3]

        return {
            "total_cavi": len(cavi),
            "cavi_attivi": len(cavi_attivi),
            "cavi_spare": len(cavi_spare),
            "cavi": cavi[:10],  # Limita a 10 cavi per non sovraccaricare la risposta
            "row_type": str(row_type),
            "row_example": str(rows[0]) if rows else "Nessuna riga trovata"
        }
    except Exception as e:
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

@router.get("/debug/raw/{cantiere_id}")
def debug_cavi_raw(cantiere_id: int, db: Session = Depends(get_db)):
    """Endpoint di debug per verificare i dati grezzi dei cavi."""
    try:
        # Esegui una query SQL diretta per ottenere i cavi
        result = db.execute(
            text("""
            SELECT id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo, tipologia,
                   n_conduttori, sezione, sh, ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                   ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo, metri_teorici, metratura_reale,
                   responsabile_posa, id_bobina, stato_installazione, modificato_manualmente, timestamp
            FROM cavi
            WHERE id_cantiere = :cantiere_id
            LIMIT 1
            """),
            {"cantiere_id": cantiere_id}
        )

        # Ottieni la prima riga
        row = result.fetchone()
        if not row:
            return {"error": "Nessun cavo trovato per questo cantiere"}

        # Raccogli informazioni dettagliate sulla riga
        row_info = {
            "type": str(type(row)),
            "dir": dir(row),
            "has_mapping": hasattr(row, '_mapping'),
            "has_fields": hasattr(row, '_fields'),
            "is_tuple": isinstance(row, tuple),
            "is_list": isinstance(row, list),
            "length": len(row) if hasattr(row, '__len__') else "N/A",
            "keys": list(result.keys()),
            "values": list(row) if hasattr(row, '__iter__') else "N/A",
            "as_string": str(row)
        }

        # Prova diversi metodi di conversione
        conversion_attempts = {}
        try:
            conversion_attempts["dict_direct"] = dict(row)
        except Exception as e:
            conversion_attempts["dict_direct_error"] = str(e)

        # Prova con _mapping (metodo più affidabile)

        if hasattr(row, '_mapping'):
            try:
                conversion_attempts["mapping"] = dict(row._mapping)
            except Exception as e:
                conversion_attempts["mapping_error"] = str(e)

        if hasattr(row, '_fields'):
            try:
                conversion_attempts["asdict"] = row._asdict()
            except Exception as e:
                conversion_attempts["asdict_error"] = str(e)

        if isinstance(row, (list, tuple)):
            try:
                column_names = result.keys()
                conversion_attempts["zip"] = dict(zip(column_names, row))
            except Exception as e:
                conversion_attempts["zip_error"] = str(e)

        return {
            "row_info": row_info,
            "conversion_attempts": conversion_attempts
        }
    except Exception as e:
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

@router.get("/{cantiere_id}/installati", response_model=List[CavoInDB])
def get_cavi_installati(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene la lista dei cavi installati di un cantiere.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[CavoInDB]: Lista dei cavi installati
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Usa la funzione del modulo collegamenti_webapp
        from backend.modules.collegamenti_webapp import get_cavi_installati
        cavi = get_cavi_installati(db, cantiere_id)
        return cavi
    except HTTPException as he:
        # Rilancia le eccezioni HTTP
        raise he
    except Exception as e:
        print(f"Errore durante il recupero dei cavi installati: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il recupero dei cavi installati: {str(e)}"
        )


@router.get("/{cantiere_id}/{cavo_id:path}", response_model=CavoInDB)
def get_cavo_by_id(
    cantiere_id: int,
    cavo_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene un cavo specifico per ID.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo trovato
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente abbia accesso al cantiere
    if current_user.ruolo != "owner" and current_user.id_utente != cantiere.id_utente:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai il permesso di accedere a questo cantiere"
        )

    # Ottieni il cavo
    cavo = db.query(Cavo).filter(
        Cavo.id_cavo == cavo_id,
        Cavo.id_cantiere == cantiere_id
    ).first()

    if not cavo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
        )

    return cavo

@router.get("/{cantiere_id}/check/{cavo_id}")
def check_cavo_exists(
    cantiere_id: int,
    cavo_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Verifica se un cavo esiste nel database.
    Utile per verificare lo stato dopo un errore di comunicazione.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Stato dell'esistenza del cavo
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente abbia accesso al cantiere
    if current_user.ruolo != "owner" and current_user.id_utente != cantiere.id_utente:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai il permesso di accedere a questo cantiere"
        )

    # Verifica se il cavo esiste
    cavo = db.query(Cavo).filter(
        Cavo.id_cavo == cavo_id,
        Cavo.id_cantiere == cantiere_id
    ).first()

    return {"exists": cavo is not None}

@router.get("/{cantiere_id}", response_model=List[CavoInDB])
def get_cavi(
    cantiere_id: int,
    tipo_cavo: Optional[int] = Query(None, description="Tipo di cavo (0 = attivo, 3 = spare)"),
    stato_installazione: Optional[str] = Query(None, description="Filtra per stato di installazione (es. 'POSATO', 'DA POSARE')"),
    tipologia: Optional[str] = Query(None, description="Filtra per tipologia di cavo"),
    sort_by: Optional[str] = Query(None, description="Campo per ordinamento (es. 'id_cavo', 'metratura_reale', 'stato_installazione')"),
    sort_order: Optional[str] = Query('asc', description="Ordine di ordinamento ('asc' o 'desc')"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene la lista dei cavi di un cantiere.

    Args:
        cantiere_id: ID del cantiere
        tipo_cavo: Tipo di cavo (0 = attivo, 3 = spare)
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[CavoInDB]: Lista dei cavi del cantiere

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    print(f"API get_cavi chiamata con cantiere_id={cantiere_id}, tipo_cavo={tipo_cavo}, stato_installazione={stato_installazione}, tipologia={tipologia}, sort_by={sort_by}, sort_order={sort_order}, utente={current_user.username}")

    try:
        print(f"Tipo di cantiere_id: {type(cantiere_id)}, valore: {cantiere_id}")

        # Assicurati che cantiere_id sia un intero
        try:
            cantiere_id_int = int(cantiere_id)
            print(f"cantiere_id convertito a intero: {cantiere_id_int}")
        except (ValueError, TypeError) as e:
            print(f"Errore nella conversione di cantiere_id a intero: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"ID cantiere non valido: {cantiere_id}. Deve essere un numero intero."
            )

        # Verifica che il cantiere esista
        try:
            cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id_int).first()
            print(f"Query cantiere eseguita: SELECT * FROM cantieri WHERE id_cantiere = {cantiere_id_int}")
        except Exception as db_error:
            print(f"Errore durante la query del cantiere: {str(db_error)}")
            import traceback
            traceback.print_exc()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Errore database: {str(db_error)}"
            )

        if not cantiere:
            print(f"Errore: Cantiere con ID {cantiere_id_int} non trovato")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id_int} non trovato"
            )

        print(f"Cantiere trovato: {cantiere.nome} (ID: {cantiere.id_cantiere})")

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        print(f"Verifica permessi: utente_id={current_user.id_utente}, cantiere.id_utente={cantiere.id_utente}, ruolo={current_user.ruolo}")
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            print(f"Errore: Utente {current_user.username} non autorizzato ad accedere al cantiere {cantiere_id_int}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Ottieni i cavi del cantiere
        try:
            print(f"Esecuzione query cavi per cantiere_id={cantiere_id_int}")

            # Costruisci la query SQL direttamente, come nella CLI
            sql_query = """
                SELECT id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo, tipologia,
                       n_conduttori, sezione, sh, ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                       ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo, metri_teorici, metratura_reale,
                       responsabile_posa, id_bobina, stato_installazione, modificato_manualmente, timestamp,
                       collegamenti, responsabile_partenza, responsabile_arrivo, comanda_posa, comanda_partenza, comanda_arrivo
                FROM cavi
                WHERE id_cantiere = :cantiere_id
            """

            params = {"cantiere_id": cantiere_id_int}

            # Filtra per tipo di cavo se specificato
            if tipo_cavo is not None:
                if tipo_cavo == 0:  # Cavi attivi
                    # Allineato con la CLI: cavi attivi sono quelli con modificato_manualmente != 3
                    sql_query += " AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL)"
                    print(f"Filtro applicato: cavi attivi (modificato_manualmente != 3 OR modificato_manualmente IS NULL)")
                elif tipo_cavo == 3:  # Cavi spare
                    # Allineato con la CLI: cavi spare sono quelli con modificato_manualmente = 3
                    sql_query += " AND modificato_manualmente = 3"
                    print(f"Filtro applicato: cavi spare (modificato_manualmente = 3)")

            # Filtra per stato di installazione se specificato
            if stato_installazione:
                # Gestisci sia i valori esatti che le corrispondenze parziali per compatibilità
                # con i diversi formati ("Installato", "POSATO", ecc.)
                if stato_installazione.lower() in ["installato", "posato"]:
                    sql_query += " AND (LOWER(stato_installazione) LIKE '%installato%' OR LOWER(stato_installazione) LIKE '%posato%')"
                    print(f"Filtro applicato: stato_installazione = Installato/POSATO")
                elif stato_installazione.lower() in ["da installare", "da posare"]:
                    sql_query += " AND (LOWER(stato_installazione) LIKE '%da installare%' OR LOWER(stato_installazione) LIKE '%da posare%')"
                    print(f"Filtro applicato: stato_installazione = Da installare/DA POSARE")
                elif stato_installazione.lower() in ["in corso", "in posa"]:
                    sql_query += " AND (LOWER(stato_installazione) LIKE '%in corso%' OR LOWER(stato_installazione) LIKE '%in posa%')"
                    print(f"Filtro applicato: stato_installazione = In corso/IN POSA")
                else:
                    # Fallback per altri valori
                    sql_query += " AND LOWER(stato_installazione) LIKE :stato_installazione"
                    params["stato_installazione"] = f"%{stato_installazione.lower()}%"
                    print(f"Filtro applicato: stato_installazione LIKE %{stato_installazione.lower()}%")

            # Filtra per tipologia se specificata
            if tipologia:
                sql_query += " AND LOWER(tipologia) LIKE :tipologia"
                params["tipologia"] = f"%{tipologia.lower()}%"
                print(f"Filtro applicato: tipologia LIKE %{tipologia.lower()}%")

            # Ordina i risultati
            if sort_by and sort_by in ['id_cavo', 'metratura_reale', 'metri_teorici', 'stato_installazione', 'tipologia', 'timestamp']:
                # Usa ORDER BY sicuro con nomi di colonna validati
                sort_direction = "DESC" if sort_order and sort_order.lower() == 'desc' else "ASC"
                sql_query += f" ORDER BY {sort_by} {sort_direction}"
                print(f"Ordinamento applicato: {sort_by} {sort_direction}")
            else:
                # Ordinamento predefinito per id_cavo
                sql_query += " ORDER BY id_cavo"

            print(f"Query SQL generata: {sql_query}")

            # Esegui la query SQL direttamente
            result = db.execute(text(sql_query), params)

            # Converti il risultato in oggetti Cavo
            rows = result.fetchall()
            print(f"Trovati {len(rows)} cavi per il cantiere {cantiere_id_int} con filtro tipo_cavo={tipo_cavo}")

            # Debug: Stampa il tipo di dati restituito
            if rows:
                print(f"Tipo di dato restituito: {type(rows[0])}")
                print(f"Esempio di riga: {rows[0]}")

            # Converti le righe in dizionari in modo sicuro
            cavi_dict = []
            column_names = result.keys()  # Ottieni i nomi delle colonne una sola volta
            print(f"Nomi delle colonne: {column_names}")

            for row in rows:
                try:
                    # Usa _mapping per la conversione (metodo più affidabile)
                    if hasattr(row, '_mapping'):
                        cavo_dict = dict(row._mapping)
                    elif isinstance(row, tuple) and hasattr(row, '_fields'):
                        # NamedTuple
                        cavo_dict = row._asdict()
                    elif isinstance(row, (list, tuple)):
                        # Crea un dizionario usando i nomi delle colonne
                        if len(column_names) == len(row):
                            cavo_dict = dict(zip(column_names, row))
                        else:
                            print(f"ERRORE: Il numero di colonne ({len(column_names)}) non corrisponde al numero di valori ({len(row)})")
                            print(f"Colonne: {column_names}")
                            print(f"Valori: {row}")
                            continue
                    else:
                        # Fallback: stampa informazioni dettagliate e salta
                        print(f"ATTENZIONE: Impossibile convertire la riga in dizionario: {row}")
                        print(f"Tipo: {type(row)}, Attributi: {dir(row) if hasattr(row, '__dict__') else 'N/A'}")
                        continue
                    cavi_dict.append(cavo_dict)
                except Exception as e:
                    print(f"Errore durante la conversione della riga in dizionario: {e}")
                    print(f"Riga problematica: {row}")
                    import traceback
                    traceback.print_exc()

            print(f"Convertiti {len(cavi_dict)} cavi in dizionari")

            # Crea oggetti Cavo dai dizionari
            cavi = []
            for i, cavo_dict in enumerate(cavi_dict):
                try:
                    # Crea un nuovo oggetto Cavo usando il costruttore con i parametri corretti
                    # Questo è più sicuro che impostare gli attributi uno per uno
                    cavo_params = {}
                    for key, value in cavo_dict.items():
                        if hasattr(Cavo, key) or key in ['id_cavo', 'id_cantiere', 'revisione_ufficiale', 'sistema',
                                                        'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione',
                                                        'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza',
                                                        'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo',
                                                        'metri_teorici', 'metratura_reale', 'responsabile_posa', 'id_bobina',
                                                        'stato_installazione', 'modificato_manualmente', 'timestamp',
                                                        'collegamenti', 'responsabile_partenza', 'responsabile_arrivo',
                                                        'comanda_posa', 'comanda_partenza', 'comanda_arrivo']:
                            cavo_params[key] = value
                        else:
                            print(f"ATTENZIONE: L'attributo '{key}' non esiste nel modello Cavo e verrà ignorato")

                    # Converti n_conduttori in stringa se è un intero
                    if 'n_conduttori' in cavo_params and isinstance(cavo_params['n_conduttori'], int):
                        cavo_params['n_conduttori'] = str(cavo_params['n_conduttori'])

                    # Crea l'oggetto Cavo
                    cavo = Cavo(**cavo_params)
                    cavi.append(cavo)
                except Exception as e:
                    print(f"Errore durante la creazione dell'oggetto Cavo #{i}: {e}")
                    print(f"Dizionario problematico: {cavo_dict}")
                    import traceback
                    traceback.print_exc()

                    # Fallback: crea un oggetto Cavo vuoto e imposta gli attributi uno per uno
                    try:
                        print("Tentativo di fallback: creazione oggetto Cavo vuoto e impostazione attributi uno per uno")
                        cavo = Cavo()
                        for key, value in cavo_dict.items():
                            if hasattr(cavo, key):
                                # Converti n_conduttori in stringa se è un intero
                                if key == 'n_conduttori' and isinstance(value, int):
                                    value = str(value)
                                setattr(cavo, key, value)
                        cavi.append(cavo)
                        print("Fallback riuscito")
                    except Exception as fallback_error:
                        print(f"Anche il fallback è fallito: {fallback_error}")

            # Stampa i primi 5 cavi trovati (se presenti)
            if cavi:
                print("Primi cavi trovati:")
                for i, cavo in enumerate(cavi[:5]):
                    print(f"  {i+1}. ID: {cavo.id_cavo}, Stato: {cavo.stato_installazione}")
            else:
                print("Nessun cavo trovato per questo cantiere con i filtri specificati")

        except Exception as cavi_error:
            print(f"Errore durante la query dei cavi: {str(cavi_error)}")
            import traceback
            traceback.print_exc()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Errore nel recupero dei cavi: {str(cavi_error)}"
            )

        return cavi
    except HTTPException as he:
        # Rilancia le eccezioni HTTP
        raise he
    except Exception as e:
        print(f"Errore imprevisto in get_cavi: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore interno del server: {str(e)}"
        )

@router.post("/{cantiere_id}", response_model=CavoInDB)
def create_cavo(
    cantiere_id: int,
    cavo_in: CavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea un nuovo cavo in un cantiere.

    Args:
        cantiere_id: ID del cantiere
        cavo_in: Dati del nuovo cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo creato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Validazione dei campi obbligatori
    if not cavo_in.id_cavo:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="L'ID del cavo è obbligatorio"
        )

    if not cavo_in.tipologia:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="La tipologia del cavo è obbligatoria"
        )

    if not cavo_in.ubicazione_partenza:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="L'ubicazione di partenza è obbligatoria"
        )

    if not cavo_in.ubicazione_arrivo:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="L'ubicazione di arrivo è obbligatoria"
        )

    # Verifica che l'ID del cavo non sia già presente nel cantiere
    existing_cavo = db.query(Cavo).filter(
        Cavo.id_cavo == cavo_in.id_cavo,
        Cavo.id_cantiere == cantiere_id
    ).first()

    if existing_cavo:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cavo con ID {cavo_in.id_cavo} già presente nel cantiere"
        )

    # Utilizziamo un blocco try-except per gestire le transazioni in modo sicuro
    try:
        # Verifica nuovamente che il cavo non esista già (per evitare race condition)
        existing_cavo = db.query(Cavo).filter(
            Cavo.id_cavo == cavo_in.id_cavo,
            Cavo.id_cantiere == cantiere_id
        ).with_for_update().first()

        if existing_cavo:
            # Se il cavo esiste già, facciamo rollback e restituiamo un errore
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Cavo con ID {cavo_in.id_cavo} già presente nel cantiere"
            )

        # Normalizza automaticamente tipologia e sezione
        normalized_tipologia, normalized_sezione = normalize_cable_data(
            cavo_in.tipologia,
            cavo_in.sezione or ""
        )

        # Crea il nuovo cavo con dati normalizzati
        cavo = Cavo(
            id_cavo=cavo_in.id_cavo,
            id_cantiere=cantiere_id,
            revisione_ufficiale=cavo_in.revisione_ufficiale,
            sistema=cavo_in.sistema,
            utility=cavo_in.utility,
            colore_cavo=cavo_in.colore_cavo,
            tipologia=normalized_tipologia,  # Usa la tipologia normalizzata
            # Converti n_conduttori in stringa se è un intero
            n_conduttori=str(cavo_in.n_conduttori) if cavo_in.n_conduttori is not None else None,
            sezione=normalized_sezione,  # Usa la sezione normalizzata
            sh=cavo_in.sh,  # Nota: ora utilizziamo sh invece di SH
            ubicazione_partenza=cavo_in.ubicazione_partenza,
            utenza_partenza=cavo_in.utenza_partenza,
            descrizione_utenza_partenza=cavo_in.descrizione_utenza_partenza,
            ubicazione_arrivo=cavo_in.ubicazione_arrivo,
            utenza_arrivo=cavo_in.utenza_arrivo,
            descrizione_utenza_arrivo=cavo_in.descrizione_utenza_arrivo,
            metri_teorici=cavo_in.metri_teorici,
            metratura_reale=cavo_in.metratura_reale if cavo_in.metratura_reale is not None else 0,
            responsabile_posa=cavo_in.responsabile_posa,
            id_bobina=cavo_in.id_bobina,
            stato_installazione=cavo_in.stato_installazione if cavo_in.stato_installazione else "Da installare",
            modificato_manualmente=0,  # 0 = non modificato manualmente (invece di False)
            timestamp=datetime.now(),
            collegamenti=0,
            responsabile_partenza=cavo_in.responsabile_partenza,
            responsabile_arrivo=cavo_in.responsabile_arrivo,
            comanda_posa=cavo_in.comanda_posa,
            comanda_partenza=cavo_in.comanda_partenza,
            comanda_arrivo=cavo_in.comanda_arrivo
        )

        # Salva il cavo nel database
        db.add(cavo)
        db.commit()
        db.refresh(cavo)

        print(f"Cavo {cavo_in.id_cavo} creato con successo nel cantiere {cantiere_id}")
        return cavo
    except HTTPException as http_ex:
        # Rilanciamo le eccezioni HTTP senza modificarle
        raise
    except Exception as e:
        # Per qualsiasi altro errore, facciamo rollback e restituiamo un errore 500
        db.rollback()
        print(f"Errore durante la creazione del cavo {cavo_in.id_cavo}: {str(e)}")

        # Verifica se il cavo è stato comunque creato nonostante l'errore
        try:
            # Verifica se il cavo esiste nel database
            verification_cavo = db.query(Cavo).filter(
                Cavo.id_cavo == cavo_in.id_cavo,
                Cavo.id_cantiere == cantiere_id
            ).first()

            if verification_cavo:
                print(f"Nonostante l'errore, il cavo {cavo_in.id_cavo} risulta creato nel database")
                db.refresh(verification_cavo)
                return verification_cavo
        except Exception as verify_error:
            print(f"Errore durante la verifica post-errore: {str(verify_error)}")

        # Se arriviamo qui, la creazione è fallita
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la creazione del cavo: {str(e)}"
        )

@router.get("/{cantiere_id}/{cavo_id}", response_model=CavoInDB)
def get_cavo(
    cantiere_id: int,
    cavo_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene i dettagli di un cavo specifico.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Dettagli del cavo

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cavo non esiste
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Ottieni il cavo dal database
    cavo = db.query(Cavo).filter(
        Cavo.id_cavo == cavo_id,
        Cavo.id_cantiere == cantiere_id
    ).first()

    # Verifica che il cavo esista
    if not cavo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
        )

    return cavo

@router.put("/{cantiere_id}/{cavo_id}", response_model=CavoInDB)
def update_cavo(
    cantiere_id: int,
    cavo_id: str,
    cavo_in: CavoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna un cavo esistente.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        cavo_in: Dati aggiornati del cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo aggiornato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cavo non esiste
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che l'utente sia il proprietario del cantiere o un amministratore
    if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Non hai i permessi per accedere a questo cantiere"
        )

    # Ottieni il cavo dal database
    cavo = db.query(Cavo).filter(
        Cavo.id_cavo == cavo_id,
        Cavo.id_cantiere == cantiere_id
    ).first()

    # Verifica che il cavo esista
    if not cavo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
        )

    try:
        # Prepara i dati da aggiornare
        update_data = cavo_in.dict(exclude_unset=True)

        # Normalizza tipologia e sezione se presenti
        if 'tipologia' in update_data or 'sezione' in update_data:
            current_tipologia = update_data.get('tipologia', cavo.tipologia)
            current_sezione = update_data.get('sezione', cavo.sezione or "")

            normalized_tipologia, normalized_sezione = normalize_cable_data(
                current_tipologia, current_sezione
            )

            update_data['tipologia'] = normalized_tipologia
            update_data['sezione'] = normalized_sezione

        # Gestisci n_conduttori se presente
        if 'n_conduttori' in update_data and update_data['n_conduttori'] is not None:
            update_data['n_conduttori'] = str(update_data['n_conduttori'])

        # Salva i valori originali per la verifica post-errore
        original_values = {}
        for key in update_data.keys():
            original_values[key] = getattr(cavo, key)

        # Aggiorna i campi del cavo
        for key, value in update_data.items():
            setattr(cavo, key, value)

        # Imposta il flag modificato_manualmente a 1 (modificato manualmente)
        cavo.modificato_manualmente = 1
        cavo.timestamp = datetime.now()

        # Salva le modifiche nel database
        db.commit()
        db.refresh(cavo)

        print(f"Cavo {cavo_id} aggiornato con successo nel cantiere {cantiere_id}")
        return cavo
    except HTTPException as http_ex:
        # Rilanciamo le eccezioni HTTP senza modificarle
        raise
    except Exception as e:
        db.rollback()
        print(f"Errore durante l'aggiornamento del cavo {cavo_id}: {str(e)}")

        # Verifica se l'aggiornamento è stato comunque applicato nonostante l'errore
        try:
            # Recupera nuovamente il cavo dal database
            verification_cavo = db.query(Cavo).filter(
                Cavo.id_cavo == cavo_id,
                Cavo.id_cantiere == cantiere_id
            ).first()

            if verification_cavo:
                # Verifica se almeno uno dei campi è stato aggiornato
                changes_applied = False
                for key, new_value in update_data.items():
                    current_value = getattr(verification_cavo, key)
                    original_value = original_values.get(key)

                    # Se il valore attuale è diverso dall'originale e uguale al nuovo valore
                    if str(current_value) != str(original_value) and str(current_value) == str(new_value):
                        changes_applied = True
                        break

                if changes_applied:
                    print(f"Nonostante l'errore, il cavo {cavo_id} risulta aggiornato nel database")
                    db.refresh(verification_cavo)
                    return verification_cavo
        except Exception as verify_error:
            print(f"Errore durante la verifica post-errore: {str(verify_error)}")

        # Se arriviamo qui, l'aggiornamento è fallito
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'aggiornamento del cavo: {str(e)}"
        )

@router.post("/{cantiere_id}/{cavo_id}/mark-as-spare", response_model=CavoInDB)
def mark_cavo_as_spare(
    cantiere_id: int,
    cavo_id: str,
    options: MarkAsSpareUpdate = Body(default=None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Marca un cavo come SPARE/consumato.
    Implementa la stessa logica della funzione _marca_cavo_come_spare nella CLI.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        options: Opzioni per la marcatura come SPARE
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo marcato come SPARE

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cavo non esiste
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Ottieni il cavo dal database
        cavo = db.query(Cavo).filter(
            Cavo.id_cavo == cavo_id,
            Cavo.id_cantiere == cantiere_id
        ).first()

        # Verifica che il cavo esista
        if not cavo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
            )

        # Verifica se il cavo è già marcato come SPARE
        if cavo.modificato_manualmente == 3:
            return cavo  # Il cavo è già SPARE, restituiscilo senza modifiche

        # Verifica se il cavo è installato e se l'opzione force è attiva
        is_installed = cavo.stato_installazione == "Installato" or (cavo.metratura_reale and cavo.metratura_reale > 0)
        force = options and options.force

        if is_installed and not force:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Il cavo risulta installato o parzialmente posato. Usa force=true per forzare la marcatura come SPARE."
            )

        # Marca il cavo come SPARE
        cavo.stato_installazione = "SPARE"
        cavo.modificato_manualmente = 3
        cavo.timestamp = datetime.now()

        # Salva le modifiche nel database
        db.commit()
        db.refresh(cavo)

        return cavo
    except HTTPException:
        # Rilancia le eccezioni HTTP senza modifiche
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la marcatura del cavo come SPARE: {str(e)}"
        )


@router.post("/{cantiere_id}/{cavo_id}/reactivate-spare", response_model=CavoInDB)
def reactivate_spare(
    cantiere_id: int,
    cavo_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Riattiva un cavo precedentemente marcato come SPARE.
    Implementa la logica per riattivare un cavo SPARE nella CLI.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo riattivato
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Verifica che il cavo esista
        cavo = db.query(Cavo).filter(
            Cavo.id_cavo == cavo_id,
            Cavo.id_cantiere == cantiere_id
        ).first()

        if not cavo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
            )

        # Verifica che il cavo sia effettivamente marcato come SPARE
        if cavo.modificato_manualmente != 3:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Il cavo {cavo_id} non è marcato come SPARE"
            )

        # Riattiva il cavo (imposta modificato_manualmente = 0)
        cavo.modificato_manualmente = 0
        cavo.stato_installazione = "Da installare"
        cavo.timestamp = datetime.now()

        # Salva le modifiche nel database
        db.commit()
        db.refresh(cavo)

        print(f"Cavo {cavo_id} riattivato nel cantiere {cantiere_id}")
        return cavo

    except HTTPException as http_ex:
        # Rilanciamo le eccezioni HTTP senza modificarle
        raise
    except Exception as e:
        db.rollback()
        print(f"Errore durante la riattivazione del cavo {cavo_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la riattivazione del cavo: {str(e)}"
        )

@router.delete("/{cantiere_id}/{cavo_id}", response_model=dict)
def delete_cavo(
    cantiere_id: int,
    cavo_id: str,
    mode: Optional[str] = Query(None, description="Modalità di eliminazione: 'spare' per marcare come SPARE, 'delete' per eliminare definitivamente"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina un cavo o lo marca come SPARE.
    Implementa la stessa logica della funzione elimina_cavo nella CLI.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        mode: Modalità di eliminazione ('spare' o 'delete')
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        dict: Messaggio di conferma

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cavo non esiste
    """
    # Log per debug
    print(f"DELETE CAVO chiamato con: cantiere_id={cantiere_id}, cavo_id={cavo_id}, mode={mode}")

    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Ottieni il cavo dal database
        cavo = db.query(Cavo).filter(
            Cavo.id_cavo == cavo_id,
            Cavo.id_cantiere == cantiere_id
        ).first()

        # Verifica che il cavo esista
        if not cavo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
            )

        # Verifica se il cavo è installato
        is_installed = cavo.stato_installazione == "Installato" or (cavo.metratura_reale and cavo.metratura_reale > 0)

        # Se il cavo è installato e non è specificata la modalità, o se è richiesta l'eliminazione definitiva,
        # impedisci l'eliminazione e suggerisci di marcarlo come SPARE
        if is_installed and (not mode or mode == 'delete'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Il cavo risulta installato o parzialmente posato. Usa mode='spare' per marcarlo come SPARE invece di eliminarlo."
            )

        # Log per debug
        print(f"Operazione richiesta: {'SPARE' if mode == 'spare' else 'DELETE'}")

        # Se è richiesta la marcatura come SPARE o il cavo è installato
        if mode == 'spare':
            # Log per debug
            print(f"Marcatura del cavo {cavo_id} come SPARE (modificato_manualmente=3)")

            # Marca il cavo come SPARE
            cavo.stato_installazione = "SPARE"
            cavo.modificato_manualmente = 3
            cavo.timestamp = datetime.now()

            # Salva le modifiche nel database
            db.commit()

            # Verifica che il cavo sia stato effettivamente marcato come SPARE
            check_cavo = db.query(Cavo).filter(
                Cavo.id_cavo == cavo_id,
                Cavo.id_cantiere == cantiere_id
            ).first()
            print(f"Verifica dopo marcatura SPARE: modificato_manualmente = {check_cavo.modificato_manualmente if check_cavo else 'N/A'}")

            return {"message": f"Cavo {cavo_id} marcato come SPARE con successo"}
        else:
            # Elimina tutte le certificazioni del cavo
            db.query(CertificazioneCavo).filter(
                CertificazioneCavo.id_cantiere == cantiere_id,
                CertificazioneCavo.id_cavo == cavo_id
            ).delete(synchronize_session=False)

            # Log per debug
            print(f"Eliminazione definitiva del cavo {cavo_id} dal database")

            # Elimina il cavo
            db.delete(cavo)

            # Commit delle modifiche
            db.commit()

            # Verifica che il cavo sia stato effettivamente eliminato
            check_cavo = db.query(Cavo).filter(
                Cavo.id_cavo == cavo_id,
                Cavo.id_cantiere == cantiere_id
            ).first()
            print(f"Verifica dopo eliminazione: cavo ancora presente = {check_cavo is not None}")

            return {"message": f"Cavo {cavo_id} eliminato con successo"}
    except HTTPException:
        # Rilancia le eccezioni HTTP senza modifiche
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'operazione: {str(e)}"
        )

# Costanti per gli stati di installazione e bobina
STATO_INSTALLATO = "Installato"
STATO_IN_CORSO = "In corso"
STATO_DA_INSTALLARE = "Da installare"
STATO_SPARE = "SPARE"

STATO_BOBINA_DISPONIBILE = "Disponibile"
STATO_BOBINA_IN_USO = "In uso"
STATO_BOBINA_TERMINATA = "Terminata"
STATO_BOBINA_OVER = "Over"

@router.post("/{cantiere_id}/{cavo_id}/metri-posati", response_model=CavoInDB)
def update_metri_posati(
    cantiere_id: int,
    cavo_id: str,
    metri_posati_in: MetriPosatiUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna i metri posati di un cavo e gestisce l'associazione con una bobina.
    Implementa la stessa logica della funzione aggiorna_metri_posati e input_metri_posati nella CLI.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        metri_posati_in: Nuovi metri posati e opzionalmente una bobina da associare
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo aggiornato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cavo non esiste
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Ottieni il cavo dal database
        cavo = db.query(Cavo).filter(
            Cavo.id_cavo == cavo_id,
            Cavo.id_cantiere == cantiere_id
        ).first()

        # Verifica che il cavo esista
        if not cavo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
            )

        # Verifica che il cavo non sia SPARE
        if cavo.modificato_manualmente == 3 or cavo.stato_installazione == STATO_SPARE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Il cavo {cavo_id} è marcato come SPARE. Riattivarlo prima di aggiornare i metri posati."
            )

        # Verifica che i metri posati siano un valore valido
        if metri_posati_in.metri_posati < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="I metri posati non possono essere negativi"
            )

        # Gestione per cavi già posati: restituisci i metri alla bobina precedente
        metri_precedenti = 0
        bobina_precedente_id = None
        if cavo.metratura_reale and cavo.metratura_reale > 0:
            metri_precedenti = float(cavo.metratura_reale)
            bobina_precedente_id = cavo.id_bobina
            print(f"Cavo {cavo_id} già posato con {metri_precedenti}m, bobina precedente: {bobina_precedente_id}")

        # Aggiorna i metri posati
        cavo.metratura_reale = metri_posati_in.metri_posati
        cavo.timestamp = datetime.now()

        # Registra la data di posa
        data_posa = metri_posati_in.data_posa or datetime.now().date()
        cavo.data_posa = data_posa

        # Determina lo stato di installazione in base ai metri posati
        if metri_posati_in.metri_posati > 0:
            # Imposta sempre lo stato a INSTALLATO quando si posano metri
            # indipendentemente dal confronto con i metri teorici
            cavo.stato_installazione = STATO_INSTALLATO
        else:
            cavo.stato_installazione = STATO_DA_INSTALLARE

        # Se il cavo aveva una bobina precedente, restituisci i metri
        if metri_precedenti > 0 and bobina_precedente_id and bobina_precedente_id != 'BOBINA_VUOTA':
            bobina_precedente = db.query(ParcoCavo).filter(
                ParcoCavo.id_bobina == bobina_precedente_id,
                ParcoCavo.id_cantiere == cantiere_id
            ).first()

            if bobina_precedente:
                # Restituisci i metri alla bobina precedente
                bobina_precedente.metri_residui += metri_precedenti

                # Aggiorna lo stato della bobina precedente
                if bobina_precedente.metri_residui < 0:
                    bobina_precedente.stato_bobina = STATO_BOBINA_OVER
                elif bobina_precedente.metri_residui == 0:
                    bobina_precedente.stato_bobina = STATO_BOBINA_TERMINATA
                elif bobina_precedente.metri_residui < bobina_precedente.metri_totali:
                    bobina_precedente.stato_bobina = STATO_BOBINA_IN_USO
                else:
                    bobina_precedente.stato_bobina = STATO_BOBINA_DISPONIBILE

                print(f"Restituiti {metri_precedenti}m alla bobina precedente {bobina_precedente_id}")
                print(f"Stato bobina precedente aggiornato: {bobina_precedente.stato_bobina} (metri residui: {bobina_precedente.metri_residui})")

        # Gestione della bobina
        print(f"Valore id_bobina ricevuto: {metri_posati_in.id_bobina}, tipo: {type(metri_posati_in.id_bobina)}")

        # Gestione esplicita di tutti i casi possibili per id_bobina
        if metri_posati_in.id_bobina is None:
            # Se id_bobina è None e il cavo è 'Da installare', imposta id_bobina a NULL
            if cavo.stato_installazione == STATO_DA_INSTALLARE or metri_posati_in.metri_posati == 0:
                cavo.id_bobina = None
                print(f"Cavo {cavo_id} non posato, id_bobina impostato a NULL")
            else:
                # Se il cavo è posato ma non è specificata una bobina, richiedi una bobina
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Per un cavo posato è necessario specificare una bobina o usare 'BOBINA_VUOTA'"
                )
        elif metri_posati_in.id_bobina == 'BOBINA_VUOTA':
            # Gestione speciale per BOBINA_VUOTA: imposta id_bobina a 'BOBINA_VUOTA'
            # per mantenere la compatibilità con la CLI originale
            if metri_posati_in.metri_posati > 0:
                cavo.id_bobina = 'BOBINA_VUOTA'
                print(f"Cavo {cavo_id} associato a BOBINA_VUOTA")
            else:
                # Se i metri posati sono 0, non permettere BOBINA_VUOTA
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Non è possibile associare BOBINA_VUOTA a un cavo non posato"
                )
        else:
            # Se è specificata una bobina reale, associala al cavo
            bobina_id = metri_posati_in.id_bobina
            print(f"Bobina ID da associare: {bobina_id}")

            # Verifica se l'ID bobina è già nel formato completo
            if not bobina_id.startswith(f"C{cantiere_id}_B"):
                bobina_id = f"C{cantiere_id}_B{bobina_id}"

            # Verifica che la bobina esista
            bobina = db.query(ParcoCavo).filter(
                ParcoCavo.id_bobina == bobina_id,
                ParcoCavo.id_cantiere == cantiere_id
            ).first()

            if not bobina:
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Bobina con ID {bobina_id} non trovata nel cantiere {cantiere_id}"
                )

            # Verifica compatibilità tra cavo e bobina
            # Nota: n_conduttori non è più utilizzato per la compatibilità
            if (bobina.tipologia != cavo.tipologia or
                str(bobina.sezione) != str(cavo.sezione)):
                # Se force_over è True, permetti l'operazione anche se la bobina non è compatibile
                if not metri_posati_in.force_over:
                    db.rollback()
                    # Restituisci un errore dettagliato con le differenze
                    incompatibilities = []
                    if bobina.tipologia != cavo.tipologia:
                        incompatibilities.append(f"Tipologia: cavo={cavo.tipologia}, bobina={bobina.tipologia}")
                    if str(bobina.sezione) != str(cavo.sezione):
                        incompatibilities.append(f"Formazione: cavo={cavo.sezione}, bobina={bobina.sezione}")

                    detail = f"La bobina {bobina_id} non è compatibile con il cavo {cavo_id}. Differenze: {', '.join(incompatibilities)}"
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=detail
                    )
                else:
                    # Se force_over è True, registra un avviso ma continua
                    print(f"ATTENZIONE: Forzata associazione di bobina incompatibile {bobina_id} con cavo {cavo_id}")
                    # Aggiorna le caratteristiche del cavo per renderlo compatibile con la bobina
                    # Questo è necessario per mantenere la coerenza dei dati
                    cavo.tipologia = bobina.tipologia
                    cavo.sezione = bobina.sezione
                    cavo.modificato_manualmente = 1  # Imposta il flag modificato_manualmente a 1
                    print(f"Caratteristiche del cavo {cavo_id} aggiornate automaticamente per compatibilità con bobina {bobina_id}")

            # Verifica se ci sono metri residui sufficienti
            if bobina.metri_residui < metri_posati_in.metri_posati:
                # Se force_over è True, permetti l'operazione anche se i metri residui sono insufficienti
                if not metri_posati_in.force_over:
                    db.rollback()
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"La bobina {bobina_id} ha solo {bobina.metri_residui}m residui, ma si sta cercando di posare {metri_posati_in.metri_posati}m. Utilizzare force_over=true per forzare l'operazione."
                    )
                else:
                    # Se force_over è True, registra un avviso ma continua
                    print(f"ATTENZIONE: Forzata posa di {metri_posati_in.metri_posati}m con bobina {bobina_id} che ha solo {bobina.metri_residui}m residui")

            # Sottrai i metri posati dai metri residui della bobina
            bobina.metri_residui -= metri_posati_in.metri_posati

            # Aggiorna lo stato della bobina
            if bobina.metri_residui < 0:
                bobina.stato_bobina = STATO_BOBINA_OVER
            elif bobina.metri_residui == 0:
                bobina.stato_bobina = STATO_BOBINA_TERMINATA
            elif bobina.metri_residui < bobina.metri_totali:
                bobina.stato_bobina = STATO_BOBINA_IN_USO
            else:
                bobina.stato_bobina = STATO_BOBINA_DISPONIBILE

            print(f"Stato bobina aggiornato: {bobina.stato_bobina} (metri residui: {bobina.metri_residui}, metri totali: {bobina.metri_totali})")

            # Associa la bobina al cavo
            cavo.id_bobina = bobina_id

        # Salva le modifiche nel database
        db.commit()
        db.refresh(cavo)

        print(f"Metri posati aggiornati per il cavo {cavo_id} nel cantiere {cantiere_id}: {metri_posati_in.metri_posati}m")
        if metri_posati_in.id_bobina:
            print(f"Bobina {metri_posati_in.id_bobina} associata al cavo {cavo_id}")

        return cavo
    except HTTPException:
        # Rilancia le eccezioni HTTP senza modifiche
        raise
    except Exception as e:
        db.rollback()
        print(f"Errore durante l'aggiornamento dei metri posati per il cavo {cavo_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'aggiornamento dei metri posati: {str(e)}"
        )

@router.post("/{cantiere_id}/{cavo_id}/update-for-compatibility", response_model=CavoInDB)
def update_cavo_for_compatibility(
    cantiere_id: int,
    cavo_id: str,
    bobina_in: BobinaUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna le caratteristiche di un cavo per renderlo compatibile con una bobina selezionata.
    Implementa la logica della CLI originale per gestire bobine incompatibili.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        bobina_in: Bobina con cui rendere compatibile il cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo aggiornato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cavo o la bobina non esistono
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Ottieni il cavo dal database
        cavo = db.query(Cavo).filter(
            Cavo.id_cavo == cavo_id,
            Cavo.id_cantiere == cantiere_id
        ).first()

        # Verifica che il cavo esista
        if not cavo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
            )

        # Verifica che il cavo non sia già installato
        if cavo.stato_installazione == "Installato" or (cavo.metratura_reale and cavo.metratura_reale > 0):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Il cavo {cavo_id} risulta già installato o parzialmente posato. Non è possibile modificarne le caratteristiche."
            )

        # Verifica che la bobina esista
        bobina_id = bobina_in.id_bobina
        # Verifica se l'ID bobina è già nel formato completo
        if not bobina_id.startswith(f"C{cantiere_id}_B"):
            bobina_id = f"C{cantiere_id}_B{bobina_id}"

        bobina = db.query(ParcoCavo).filter(
            ParcoCavo.id_bobina == bobina_id,
            ParcoCavo.id_cantiere == cantiere_id
        ).first()

        if not bobina:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Bobina con ID {bobina_id} non trovata nel cantiere {cantiere_id}"
            )

        # Aggiorna le caratteristiche del cavo per renderlo compatibile con la bobina
        # Nota: n_conduttori non è più utilizzato per la compatibilità
        cavo.tipologia = bobina.tipologia
        cavo.sezione = bobina.sezione
        cavo.modificato_manualmente = 1  # Imposta il flag modificato_manualmente a 1
        cavo.timestamp = datetime.now()

        # Salva le modifiche nel database
        db.commit()
        db.refresh(cavo)

        print(f"Caratteristiche del cavo {cavo_id} aggiornate per compatibilità con bobina {bobina_id}")
        print(f"Nuove caratteristiche: tipologia={cavo.tipologia}, sezione={cavo.sezione}")

        return cavo
    except HTTPException:
        # Rilancia le eccezioni HTTP senza modifiche
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'aggiornamento delle caratteristiche del cavo: {str(e)}"
        )

@router.post("/{cantiere_id}/{cavo_id}/cancel-installation", response_model=CavoInDB)
@router.put("/{cantiere_id}/{cavo_id}/cancel-installation", response_model=CavoInDB)
@router.post("/{cantiere_id}/{cavo_id}/annulla-installazione", response_model=CavoInDB)
def cancel_installation(
    cantiere_id: int,
    cavo_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Annulla l'installazione di un cavo, restituendo i metri alla bobina originale.
    Implementa la stessa logica della funzione annulla_installazione_cavo nella CLI.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo con installazione annullata

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cavo non esiste
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Ottieni il cavo dal database
        cavo = db.query(Cavo).filter(
            Cavo.id_cavo == cavo_id,
            Cavo.id_cantiere == cantiere_id
        ).first()

        # Verifica che il cavo esista
        if not cavo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
            )

        # Verifica che il cavo sia installato
        if not cavo.metratura_reale or float(cavo.metratura_reale) <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Il cavo {cavo_id} non risulta installato"
            )

        # Memorizza i dati necessari prima di modificare il cavo
        metri_da_restituire = float(cavo.metratura_reale)
        id_bobina = cavo.id_bobina

        # Se il cavo ha una bobina associata (non BOBINA_VUOTA), restituisci i metri alla bobina
        if id_bobina and id_bobina != "BOBINA_VUOTA" and id_bobina != "TBD":
            # Verifica che la bobina esista
            bobina = db.query(ParcoCavo).filter(
                ParcoCavo.id_bobina == id_bobina,
                ParcoCavo.id_cantiere == cantiere_id
            ).first()

            if bobina:
                # Restituisci i metri alla bobina
                bobina.metri_residui += metri_da_restituire

                # Aggiorna lo stato della bobina
                if bobina.metri_residui < 0:
                    bobina.stato_bobina = "Over"
                elif bobina.metri_residui == 0:
                    bobina.stato_bobina = "Terminata"
                elif bobina.metri_residui < bobina.metri_totali:
                    bobina.stato_bobina = "In uso"
                else:
                    bobina.stato_bobina = "Disponibile"

                print(f"Restituiti {metri_da_restituire} metri alla bobina {id_bobina}")

        # Aggiorna lo stato del cavo a DA_INSTALLARE, azzera la metratura reale e rimuovi la data di posa
        cavo.stato_installazione = "Da installare"
        cavo.metratura_reale = 0
        cavo.id_bobina = None
        cavo.data_posa = None
        cavo.timestamp = datetime.now()

        # Salva le modifiche nel database
        db.commit()
        db.refresh(cavo)

        print(f"Installazione del cavo {cavo_id} annullata con successo")
        return cavo
    except HTTPException:
        # Rilancia le eccezioni HTTP senza modifiche
        raise
    except Exception as e:
        db.rollback()
        print(f"Errore durante l'annullamento dell'installazione del cavo {cavo_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'annullamento dell'installazione: {str(e)}"
        )

@router.post("/{cantiere_id}/{cavo_id}/bobina", response_model=CavoInDB)
def update_bobina(
    cantiere_id: int,
    cavo_id: str,
    bobina_in: BobinaUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna la bobina di un cavo.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        bobina_in: Nuova bobina
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Cavo aggiornato

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cavo non esiste
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Ottieni il cavo dal database
        cavo = db.query(Cavo).filter(
            Cavo.id_cavo == cavo_id,
            Cavo.id_cantiere == cantiere_id
        ).first()

        # Verifica che il cavo esista
        if not cavo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cavo con ID {cavo_id} non trovato nel cantiere {cantiere_id}"
            )

        # Verifica che il cavo sia posato
        if not cavo.metratura_reale or cavo.metratura_reale <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Il cavo {cavo_id} non risulta posato. Utilizzare la funzione 'Aggiorna metri posati'."
            )

        # Salva la vecchia bobina per aggiornare i metri residui
        vecchia_bobina_id = cavo.id_bobina
        metri_posati = cavo.metratura_reale

        # Verifica che la bobina esista nel parco cavi del cantiere
        if bobina_in.id_bobina and bobina_in.id_bobina != 'BOBINA_VUOTA':
            # Verifica se l'ID bobina è già nel formato completo
            bobina_id = bobina_in.id_bobina
            if not bobina_id.startswith(f"C{cantiere_id}_B"):
                bobina_id = f"C{cantiere_id}_B{bobina_id}"

            bobina = db.query(ParcoCavo).filter(
                ParcoCavo.id_bobina == bobina_id,
                ParcoCavo.id_cantiere == cantiere_id
            ).first()

            if not bobina:
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Bobina con ID {bobina_id} non trovata nel cantiere {cantiere_id}"
                )

            # Verifica che la bobina sia disponibile
            if bobina.stato_bobina != "Disponibile" and bobina.stato_bobina != "In uso":
                db.rollback()
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"La bobina {bobina_id} non è disponibile per l'uso (stato: {bobina.stato_bobina})"
                )

            # Verifica compatibilità tra cavo e bobina
            if (bobina.tipologia != cavo.tipologia or
                str(bobina.sezione) != str(cavo.sezione)):
                # Se force_over è True, permetti l'operazione anche se la bobina non è compatibile
                if not bobina_in.force_over:
                    db.rollback()
                    # Restituisci un errore dettagliato con le differenze
                    incompatibilities = []
                    if bobina.tipologia != cavo.tipologia:
                        incompatibilities.append(f"Tipologia: cavo={cavo.tipologia}, bobina={bobina.tipologia}")
                    if str(bobina.sezione) != str(cavo.sezione):
                        incompatibilities.append(f"Formazione: cavo={cavo.sezione}, bobina={bobina.sezione}")

                    detail = f"La bobina {bobina_id} non è compatibile con il cavo {cavo_id}. Differenze: {', '.join(incompatibilities)}"
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=detail
                    )
                else:
                    # Se force_over è True, registra un avviso ma continua
                    print(f"ATTENZIONE: Forzata associazione di bobina incompatibile {bobina_id} con cavo {cavo_id}")
                    # Aggiorna le caratteristiche del cavo per renderlo compatibile con la bobina
                    cavo.tipologia = bobina.tipologia
                    cavo.sezione = bobina.sezione
                    cavo.modificato_manualmente = 1  # Imposta il flag modificato_manualmente a 1
                    print(f"Caratteristiche del cavo {cavo_id} aggiornate automaticamente per compatibilità con bobina {bobina_id}")

            # Verifica se ci sono metri residui sufficienti
            if bobina.metri_residui < metri_posati:
                # Se force_over è True, permetti l'operazione anche se i metri residui sono insufficienti
                if not bobina_in.force_over:
                    db.rollback()
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"La bobina {bobina_id} ha solo {bobina.metri_residui}m residui, ma il cavo richiede {metri_posati}m. Utilizzare force_over=true per forzare l'operazione."
                    )
                else:
                    # Se force_over è True, registra un avviso ma continua
                    print(f"ATTENZIONE: Forzata associazione di {metri_posati}m con bobina {bobina_id} che ha solo {bobina.metri_residui}m residui")

            # Sottrai i metri posati dai metri residui della bobina
            bobina.metri_residui -= metri_posati

            # Aggiorna lo stato della bobina
            if bobina.metri_residui < 0:
                bobina.stato_bobina = STATO_BOBINA_OVER
            elif bobina.metri_residui == 0:
                bobina.stato_bobina = STATO_BOBINA_TERMINATA
            elif bobina.metri_residui < bobina.metri_totali:
                bobina.stato_bobina = STATO_BOBINA_IN_USO
            else:
                bobina.stato_bobina = STATO_BOBINA_DISPONIBILE

            print(f"Stato bobina aggiornato: {bobina.stato_bobina} (metri residui: {bobina.metri_residui}, metri totali: {bobina.metri_totali})")

            # Aggiorna la bobina del cavo
            cavo.id_bobina = bobina_id
            print(f"Cavo {cavo_id} associato a bobina {bobina_id}")

        elif bobina_in.id_bobina == 'BOBINA_VUOTA':
            # Gestione speciale per BOBINA_VUOTA: imposta id_bobina a 'BOBINA_VUOTA'
            cavo.id_bobina = 'BOBINA_VUOTA'
            print(f"Cavo {cavo_id} associato a BOBINA_VUOTA")
        else:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Per un cavo posato è necessario specificare una bobina o usare 'BOBINA_VUOTA'"
            )

        # Se il cavo era associato a una bobina reale, aggiorna i metri residui della vecchia bobina
        if vecchia_bobina_id and vecchia_bobina_id != 'BOBINA_VUOTA' and vecchia_bobina_id != bobina_in.id_bobina:
            vecchia_bobina = db.query(ParcoCavo).filter(
                ParcoCavo.id_bobina == vecchia_bobina_id,
                ParcoCavo.id_cantiere == cantiere_id
            ).first()

            if vecchia_bobina:
                # Aggiungi i metri posati ai metri residui della vecchia bobina
                vecchia_bobina.metri_residui += metri_posati

                # Aggiorna lo stato della vecchia bobina
                if vecchia_bobina.metri_residui >= vecchia_bobina.metri_totali:
                    vecchia_bobina.stato_bobina = STATO_BOBINA_DISPONIBILE
                else:
                    vecchia_bobina.stato_bobina = STATO_BOBINA_IN_USO

                print(f"Stato vecchia bobina aggiornato: {vecchia_bobina.stato_bobina} (metri residui: {vecchia_bobina.metri_residui}, metri totali: {vecchia_bobina.metri_totali})")

        cavo.timestamp = datetime.now()

        # Salva le modifiche nel database
        db.commit()
        db.refresh(cavo)

        return cavo
    except HTTPException:
        # Rilancia le eccezioni HTTP senza modifiche
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante l'aggiornamento della bobina: {str(e)}"
        )

@router.get("/{cantiere_id}/stats", response_model=Dict)
def get_cavi_stats(
    cantiere_id: int,
    revisione: Optional[str] = Query(None, description="Revisione specifica (default: revisione corrente)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Ottiene statistiche riassuntive sui cavi di un cantiere.

    Logica Revisioni:
    - Se revisione non specificata: usa la revisione corrente (ultima importata)
    - Se revisione specificata: mostra statistiche per quella revisione storica
    - La revisione corrente rappresenta lo stato attuale del cablaggio

    Args:
        cantiere_id: ID del cantiere
        revisione: Revisione specifica per visualizzazione storica (opzionale)
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        Dict: Statistiche sui cavi del cantiere per la revisione specificata

    Raises:
        HTTPException: Se l'utente non ha i permessi necessari o se il cantiere non esiste
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Verifica che l'utente sia il proprietario del cantiere o un amministratore
        if cantiere.id_utente != current_user.id_utente and current_user.ruolo != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Non hai i permessi per accedere a questo cantiere"
            )

        # Determina la revisione da utilizzare
        revisione_da_usare = revisione
        if not revisione_da_usare:
            # Se non specificata, usa la revisione corrente (ultima importata)
            result_rev = db.execute(
                text("""
                    SELECT revisione_ufficiale
                    FROM cavi
                    WHERE id_cantiere = :cantiere_id
                    AND revisione_ufficiale IS NOT NULL
                    AND revisione_ufficiale != ''
                    ORDER BY timestamp DESC
                    LIMIT 1
                """),
                {"cantiere_id": cantiere_id}
            ).fetchone()

            if result_rev:
                revisione_da_usare = result_rev[0]
                logging.info(f"Usando revisione corrente: {revisione_da_usare}")
            else:
                # Nessuna revisione trovata, usa tutti i cavi
                logging.info("Nessuna revisione trovata, usando tutti i cavi")

        # Prepara i parametri per le query
        params = {"cantiere_id": cantiere_id}
        where_clause = "WHERE id_cantiere = :cantiere_id"

        # Aggiungi filtro per revisione se determinata
        if revisione_da_usare:
            where_clause += " AND revisione_ufficiale = :revisione"
            params["revisione"] = revisione_da_usare

        # Esegui query per ottenere statistiche sui cavi
        # 1. Totale cavi attivi e spare
        result_count = db.execute(
            text(f"""
            SELECT
                COUNT(*) as total_cavi,
                SUM(CASE WHEN modificato_manualmente != 3 OR modificato_manualmente IS NULL THEN 1 ELSE 0 END) as cavi_attivi,
                SUM(CASE WHEN modificato_manualmente = 3 THEN 1 ELSE 0 END) as cavi_spare
            FROM cavi
            {where_clause}
            """),
            params
        ).fetchone()

        # 2. Totale metri posati e percentuale di completamento
        result_metri = db.execute(
            text(f"""
            SELECT
                SUM(metri_teorici) as metri_teorici_totali,
                SUM(metratura_reale) as metri_reali_totali,
                CASE
                    WHEN SUM(metri_teorici) > 0 THEN
                        ROUND((SUM(metratura_reale) / SUM(metri_teorici)) * 100, 2)
                    ELSE 0
                END as percentuale_completamento
            FROM cavi
            {where_clause} AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL)
            """),
            params
        ).fetchone()

        # 3. Numero di cavi per stato di installazione
        result_stati = db.execute(
            text(f"""
            SELECT
                stato_installazione,
                COUNT(*) as count
            FROM cavi
            {where_clause} AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL)
            GROUP BY stato_installazione
            ORDER BY count DESC
            """),
            params
        ).fetchall()

        # 4. Numero di cavi per tipologia
        result_tipologie = db.execute(
            text(f"""
            SELECT
                tipologia,
                COUNT(*) as count
            FROM cavi
            {where_clause} AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL)
            GROUP BY tipologia
            ORDER BY count DESC
            """),
            params
        ).fetchall()

        # Converti i risultati in dizionari
        count_dict = dict(zip(result_count.keys(), result_count))
        metri_dict = dict(zip(result_metri.keys(), result_metri))

        stati_list = []
        for row in result_stati:
            stati_list.append({
                "stato": row.stato_installazione or "Non specificato",
                "count": row.count
            })

        tipologie_list = []
        for row in result_tipologie:
            tipologie_list.append({
                "tipologia": row.tipologia or "Non specificata",
                "count": row.count
            })

        # Prepara il risultato finale
        return {
            "revisione_utilizzata": revisione_da_usare or "Nessuna revisione",
            "revisione_richiesta": revisione,
            "totali": {
                "cavi_totali": count_dict.get("total_cavi", 0),
                "cavi_attivi": count_dict.get("cavi_attivi", 0),
                "cavi_spare": count_dict.get("cavi_spare", 0)
            },
            "metrature": {
                "metri_teorici_totali": float(metri_dict.get("metri_teorici_totali", 0) or 0),
                "metri_reali_totali": float(metri_dict.get("metri_reali_totali", 0) or 0),
                "percentuale_completamento": float(metri_dict.get("percentuale_completamento", 0) or 0)
            },
            "stati": stati_list,
            "tipologie": tipologie_list
        }
    except HTTPException as he:
        # Rilancia le eccezioni HTTP
        raise he
    except Exception as e:
        print(f"Errore durante il recupero delle statistiche: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il recupero delle statistiche: {str(e)}"
        )

@router.get("/debug/table-info", response_model=Dict)
def get_table_info(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Endpoint di debug per ottenere informazioni sulla struttura delle tabelle.
    Solo per amministratori.
    """
    if current_user.ruolo != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Solo gli amministratori possono accedere a questo endpoint"
        )

    try:
        # Ottieni informazioni sulla tabella cavi
        inspector = inspect(engine)
        cavi_columns = inspector.get_columns('cavi')
        cantieri_columns = inspector.get_columns('cantieri')
        parco_cavi_columns = inspector.get_columns('parco_cavi')

        # Esegui una query di test sulla tabella cavi
        result = db.execute(text("SELECT COUNT(*) FROM cavi")).scalar()

        # Verifica la presenza di vincoli di chiave esterna
        cavi_fks = inspector.get_foreign_keys('cavi')

        return {
            "cavi_columns": [{
                "name": col["name"],
                "type": str(col["type"]),
                "nullable": col["nullable"]
            } for col in cavi_columns],
            "cantieri_columns": [{
                "name": col["name"],
                "type": str(col["type"]),
                "nullable": col["nullable"]
            } for col in cantieri_columns],
            "parco_cavi_columns": [{
                "name": col["name"],
                "type": str(col["type"]),
                "nullable": col["nullable"]
            } for col in parco_cavi_columns],
            "cavi_count": result,
            "cavi_foreign_keys": cavi_fks
        }
    except Exception as e:
        print(f"Errore durante il recupero delle informazioni sulla tabella: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il recupero delle informazioni sulla tabella: {str(e)}"
        )


@router.get("/{cantiere_id}/installati", response_model=List[CavoInDB])
def get_cavi_installati(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutti i cavi installati di un cantiere.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[CavoInDB]: Lista dei cavi installati
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Usa la funzione del nuovo modulo
        from backend.modules.collegamenti_webapp import get_cavi_installati as get_cavi_installati_webapp
        cavi = get_cavi_installati_webapp(db, cantiere_id)

        return cavi
    except HTTPException as he:
        # Rilancia le eccezioni HTTP
        raise he
    except Exception as e:
        print(f"Errore durante il recupero dei cavi installati: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il recupero dei cavi installati: {str(e)}"
        )


@router.get("/{cantiere_id}/installati/search", response_model=List[CavoInDB])
def search_cavi_installati(
    cantiere_id: int,
    search_term: str = Query(..., description="Termine di ricerca"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Cerca cavi installati per ID.

    Args:
        cantiere_id: ID del cantiere
        search_term: Termine di ricerca
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[CavoInDB]: Lista dei cavi installati che corrispondono al termine di ricerca
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Usa la funzione del nuovo modulo
        from backend.modules.collegamenti_webapp import cerca_cavi_installati
        cavi = cerca_cavi_installati(db, cantiere_id, search_term)
        return cavi
    except HTTPException as he:
        # Rilancia le eccezioni HTTP
        raise he
    except Exception as e:
        print(f"Errore durante la ricerca dei cavi installati: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la ricerca dei cavi installati: {str(e)}"
        )


@router.get("/{cantiere_id}/installati", response_model=List[CavoInDB])
def get_cavi_installati(
    cantiere_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutti i cavi installati di un cantiere per la gestione collegamenti.

    Args:
        cantiere_id: ID del cantiere
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        List[CavoInDB]: Lista dei cavi installati
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Usa la funzione del modulo collegamenti
        from backend.modules.collegamenti_webapp import get_cavi_installati
        cavi = get_cavi_installati(db, cantiere_id)

        return cavi
    except HTTPException as he:
        # Rilancia le eccezioni HTTP
        raise he
    except Exception as e:
        print(f"Errore durante il recupero dei cavi installati: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il recupero dei cavi installati: {str(e)}"
        )


@router.post("/{cantiere_id}/{cavo_id}/collegamento", response_model=CavoInDB)
def collega_cavo(
    cantiere_id: int,
    cavo_id: str,
    collegamento: CollegamentoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Collega un lato di un cavo.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        collegamento: Dati del collegamento
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Il cavo aggiornato
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Normalizza l'ID del cavo (converti in maiuscolo)
        cavo_id = cavo_id.upper()

        # Usa la funzione del nuovo modulo
        from backend.modules.collegamenti_webapp import collega_cavo_webapp
        try:
            cavo = collega_cavo_webapp(
                db,
                cantiere_id,
                cavo_id,
                collegamento.lato,
                collegamento.responsabile
            )
            return cavo
        except ValueError as ve:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(ve)
            )
    except HTTPException as he:
        # Rilancia le eccezioni HTTP
        raise he
    except Exception as e:
        print(f"Errore durante il collegamento del cavo: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante il collegamento del cavo: {str(e)}"
        )


@router.delete("/{cantiere_id}/{cavo_id}/collegamento/{lato}", response_model=CavoInDB)
def scollega_cavo(
    cantiere_id: int,
    cavo_id: str,
    lato: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Scollega un lato di un cavo.

    Args:
        cantiere_id: ID del cantiere
        cavo_id: ID del cavo
        lato: Lato da scollegare ('partenza' o 'arrivo')
        db: Sessione del database
        current_user: Utente corrente

    Returns:
        CavoInDB: Il cavo aggiornato
    """
    try:
        # Verifica che il cantiere esista
        cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        # Normalizza l'ID del cavo (converti in maiuscolo)
        cavo_id = cavo_id.upper()

        # Usa la funzione del nuovo modulo
        from backend.modules.collegamenti_webapp import scollega_cavo_webapp
        try:
            cavo = scollega_cavo_webapp(db, cantiere_id, cavo_id, lato)
            return cavo
        except ValueError as ve:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(ve)
            )
    except HTTPException as he:
        # Rilancia le eccezioni HTTP
        raise he
    except Exception as e:
        print(f"Errore durante lo scollegamento del cavo: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante lo scollegamento del cavo: {str(e)}"
        )
